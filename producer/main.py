from xtquant import xtdata
import time
import json
import pandas as pd
import signal
import sys
import os
import argparse
from rabbitmq_client import get_rabbitmq_client

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from producer_metrics import (
    get_producer_metrics_collector,
    set_service_status,
    set_qmt_connection_status,
    set_rabbitmq_connection_status,
    record_message_sent,
    record_data_callback,
    set_subscribed_stocks_count,
    record_error
)
from producer_health import start_producer_health_server, stop_producer_health_server
from stock_config_loader import load_stock_list, get_subscription_config
import logging

# 设置日志
def setup_logging():
    """设置日志配置，解决中文乱码问题"""
    # 创建日志格式器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 获取根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)

    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)
    root_logger.addHandler(console_handler)

    # 文件处理器 - 使用UTF-8编码
    try:
        # 确保日志目录存在（使用项目根目录的绝对路径）
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        log_dir = os.path.join(project_root, 'logs')
        if not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)

        log_file_path = os.path.join(log_dir, 'producer.log')
        file_handler = logging.FileHandler(
            log_file_path,
            mode='a',
            encoding='utf-8'  # 明确指定UTF-8编码
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.INFO)
        root_logger.addHandler(file_handler)

        print(f"日志文件已配置: {log_file_path}")

    except Exception as e:
        print(f"配置文件日志处理器失败: {e}")
        print("将只使用控制台输出")

# 全局变量，用于存储配置
environment = 'dev'  # 默认开发环境
rabbitmq_config_path = None
stocks_config_path = None
code = []
subscription_config = {}
rabbitmq_client = None

# 初始化日志
setup_logging()

logger = logging.getLogger(__name__)


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='市场数据生产者')
    parser.add_argument('--env', '--environment', default='dev',
                       choices=['dev', 'prod'],
                       help='运行环境 (dev: 开发环境, prod: 生产环境)')
    parser.add_argument('--rabbitmq-config', help='RabbitMQ配置文件路径 (可选，会根据环境自动选择)')
    parser.add_argument('--stocks-config', help='股票配置文件路径 (可选，会根据环境自动选择)')
    parser.add_argument('--log-level', default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')

    args = parser.parse_args()

    # 根据环境设置默认配置文件路径（使用项目根目录的绝对路径）
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if not args.rabbitmq_config:
        args.rabbitmq_config = os.path.join(project_root, 'config', args.env, 'rabbitmq_config.yaml')
    if not args.stocks_config:
        args.stocks_config = os.path.join(project_root, 'config', args.env, 'stocks_config.yaml')

    return args


def load_stocks_from_config(stocks_config_path):
    """从配置文件加载股票列表"""
    try:
        # 加载股票列表
        stock_list = load_stock_list(config_path=stocks_config_path)
        logger.info(f"从配置文件 {stocks_config_path} 加载了 {len(stock_list)} 只股票")

        # 加载订阅配置
        sub_config = get_subscription_config(config_path=stocks_config_path)
        logger.info(f"订阅配置: period={sub_config.get('period')}, count={sub_config.get('count')}")

        return stock_list, sub_config

    except Exception as e:
        logger.error(f"从配置文件 {stocks_config_path} 加载股票失败: {e}")
        logger.warning("使用默认股票列表")

        # 备用的默认股票列表
        default_stocks = [
            '600030.SH', '600061.SH', '600109.SH', '600918.SH', '600958.SH',
            '600999.SH', '601059.SH', '601066.SH', '601108.SH', '601136.SH',
            '000166.SZ', '000776.SZ', '000783.SZ', '002673.SZ', '002736.SZ'
        ]
        default_config = {'period': '1m', 'count': 0, 'delay_between_stocks': 0.1}

        return default_stocks, default_config


def initialize_environment(args):
    """初始化环境配置"""
    global environment, rabbitmq_config_path, stocks_config_path, code, subscription_config, rabbitmq_client

    environment = args.env
    rabbitmq_config_path = args.rabbitmq_config
    stocks_config_path = args.stocks_config

    logger.info(f"🌍 运行环境: {environment.upper()}")
    logger.info(f"📁 RabbitMQ配置: {rabbitmq_config_path}")
    logger.info(f"📁 股票配置: {stocks_config_path}")

    # 设置日志级别
    logging.getLogger().setLevel(getattr(logging, args.log_level))

    # 加载股票配置
    code, subscription_config = load_stocks_from_config(stocks_config_path)

    # 初始化RabbitMQ客户端
    rabbitmq_client = get_rabbitmq_client(rabbitmq_config_path)


# 初始化监控和健康检查
def initialize_monitoring():
    """初始化监控和健康检查服务"""
    try:
        # 启动Prometheus指标收集器
        logger.info("启动Prometheus指标收集器...")
        metrics_collector = get_producer_metrics_collector(port=8001)

        # 启动健康检查服务器
        logger.info("启动健康检查服务器...")
        health_server = start_producer_health_server(host='0.0.0.0', port=8081)

        # 设置服务状态
        set_service_status(True)

        logger.info("监控服务初始化完成")
        logger.info("Prometheus指标: http://localhost:8001/metrics")
        logger.info("健康检查: http://localhost:8081/health")

        return metrics_collector, health_server

    except Exception as e:
        logger.error(f"初始化监控服务失败: {e}")
        raise

# RabbitMQ客户端将在initialize_environment中初始化

#订阅最新行情
def callback_func(data):
    """
    行情数据回调函数
    将接收到的数据发送到RabbitMQ队列
    """
    start_time = time.time()
    print(f"触发回调:{data}")
    try:
        # 提取股票代码信息用于日志
        stock_symbols = list(data.keys()) if isinstance(data, dict) else ['unknown']
        symbols_str = ', '.join(stock_symbols[:3])  # 最多显示3个股票代码
        if len(stock_symbols) > 3:
            symbols_str += f" (+{len(stock_symbols)-3} more)"

        logger.debug(f'回调触发 [{symbols_str}]: {len(str(data))} bytes')

        # 记录数据回调
        record_data_callback()

        # 发送数据到RabbitMQ
        success = rabbitmq_client.send_message(data)

        # 记录发送结果
        send_duration = time.time() - start_time
        record_message_sent(success, send_duration)

        if success:
            logger.debug(f"数据已发送到RabbitMQ [{symbols_str}]: {len(str(data))} bytes, 耗时: {send_duration:.3f}s")
        else:
            logger.error(f"发送数据到RabbitMQ失败 [{symbols_str}]")
            record_error("rabbitmq_send_failed")

    except Exception as e:
        logger.error(f"callback_func处理数据时发生错误: {e}")
        record_error("callback_processing_error")

        # 记录失败的发送
        send_duration = time.time() - start_time
        record_message_sent(False, send_duration)
    

def setup_signal_handlers():
    """设置信号处理器"""
    def signal_handler(signum, frame):
        logger.info(f"接收到信号 {signum}，开始优雅关闭...")
        cleanup_and_exit()

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def cleanup_and_exit():
    """清理资源并退出"""
    try:
        logger.info("开始清理资源...")

        # 设置服务状态为停止
        set_service_status(False)

        # 清理RabbitMQ客户端
        from rabbitmq_client import close_rabbitmq_client
        close_rabbitmq_client()

        # 停止健康检查服务器
        stop_producer_health_server()

        logger.info("资源清理完成，服务已关闭")

    except Exception as e:
        logger.error(f"清理资源时发生错误: {e}")
    finally:
        sys.exit(0)

def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_arguments()

        # 初始化环境配置
        initialize_environment(args)

        logger.info(f"启动市场数据生产者服务... (环境: {environment})")

        # 初始化监控
        metrics_collector, health_server = initialize_monitoring()

        # 设置信号处理器
        setup_signal_handlers()

        # 检查RabbitMQ连接
        try:
            # 测试RabbitMQ连接
            test_success = rabbitmq_client.send_message({"test": "connection"})
            set_rabbitmq_connection_status(test_success)

            if test_success:
                logger.info("RabbitMQ连接测试成功")
            else:
                logger.warning("RabbitMQ连接测试失败，但继续运行")

        except Exception as e:
            logger.error(f"RabbitMQ连接测试失败: {e}")
            set_rabbitmq_connection_status(False)
            record_error("rabbitmq_connection_failed")

        # 设置订阅股票数量
        logger.info(f"准备订阅 {len(code)} 只股票")

        # 订阅行情数据
        logger.info("开始订阅行情数据...")

        # 循环订阅每个股票代码
        success_count = 0
        period = subscription_config.get('period', '1m')
        count = subscription_config.get('count', 0)
        delay = subscription_config.get('delay_between_stocks', 0.1)

        logger.info(f"订阅参数: period={period}, count={count}, delay={delay}s")

        for stock_code in code:
            try:
                xtdata.subscribe_quote(stock_code, period=period, count=count, callback=callback_func)
                success_count += 1
                logger.info(f"成功订阅股票: {stock_code}")

                # 使用配置的延迟时间
                time.sleep(delay)

            except Exception as e:
                logger.error(f"订阅股票 {stock_code} 失败: {e}")
                record_error("stock_subscription_failed")

        logger.info(f"订阅完成: {success_count}/{len(code)} 只股票订阅成功")

        # 设置实际订阅成功的股票数量
        set_subscribed_stocks_count(success_count)

        # 设置QMT连接状态（假设有股票订阅成功表示连接正常）
        set_qmt_connection_status(success_count > 0)

        logger.info("数据订阅服务已启动")
        logger.info("监控端点:")
        logger.info("  健康检查: http://localhost:8081/health")
        logger.info("  系统信息: http://localhost:8081/system")
        logger.info("  Prometheus指标: http://localhost:8001/metrics")
        logger.info("按 CTRL+C 停止服务")

        # 死循环 阻塞主线程退出
        xtdata.run()

    except KeyboardInterrupt:
        logger.info("接收到键盘中断信号")
        cleanup_and_exit()
    except Exception as e:
        logger.error(f"运行过程中发生错误: {e}")
        record_error("service_runtime_error")
        set_qmt_connection_status(False)
        cleanup_and_exit()

if __name__ == '__main__':
    main()