#!/usr/bin/env python3
"""
主控制程序 - 自动启动和监控producer和consumer程序
功能：
1. 在指定时间段（8:50-15:50）内运行
2. 同时启动producer和consumer程序，分别在两个窗口中运行
3. 轮询检测程序状态，如果程序意外退出且仍在时间范围内，则自动重启
4. 支持优雅关闭和资源清理
"""

import os
import sys
import time
import signal
import logging
import subprocess
import threading
from datetime import datetime, time as dt_time, timedelta
from pathlib import Path
from typing import Dict, Optional, Tuple
import argparse


class MainController:
    """主控制器类"""
    
    def __init__(self, environment: str = 'dev', check_interval: int = 30):
        """
        初始化主控制器
        
        Args:
            environment: 运行环境 (dev/prod)
            check_interval: 检查间隔（秒）
        """
        self.environment = environment
        self.check_interval = check_interval
        self.running = False
        
        # 工作时间设置 (8:50 - 15:50)
        self.start_time = dt_time(8, 50)
        self.end_time = dt_time(18, 50)
        
        # 项目根目录
        self.project_root = Path(__file__).parent.absolute()
        
        # 进程管理
        self.processes: Dict[str, Optional[subprocess.Popen]] = {
            'producer': None,
            'consumer': None
        }
        
        # 设置日志
        self.logger = self._setup_logger()
        
        # 设置信号处理
        self._setup_signal_handlers()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志配置"""
        # 确保日志目录存在
        log_dir = self.project_root / 'logs'
        log_dir.mkdir(exist_ok=True)
        
        # 创建日志格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 获取根日志记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)
        
        # 清除现有的处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)
        root_logger.addHandler(console_handler)
        
        # 文件处理器
        try:
            log_file = log_dir / 'main_controller.log'
            file_handler = logging.FileHandler(
                log_file,
                mode='a',
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            file_handler.setLevel(logging.INFO)
            root_logger.addHandler(file_handler)
            
            print(f"日志文件已配置: {log_file}")
            
        except Exception as e:
            print(f"配置文件日志处理器失败: {e}")
            print("将只使用控制台输出")
        
        return logging.getLogger(__name__)
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"接收到信号 {signum}，开始优雅关闭...")
            self.shutdown()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def is_working_hours(self) -> bool:
        """检查当前是否在工作时间内"""
        current_time = datetime.now().time()
        return self.start_time <= current_time <= self.end_time
    
    def get_time_until_start(self) -> Optional[int]:
        """获取距离开始时间的秒数，如果已经过了开始时间则返回None"""
        now = datetime.now()
        current_time = now.time()
        
        if current_time < self.start_time:
            # 今天还没到开始时间
            start_datetime = now.replace(
                hour=self.start_time.hour,
                minute=self.start_time.minute,
                second=0,
                microsecond=0
            )
            return int((start_datetime - now).total_seconds())
        elif current_time > self.end_time:
            # 今天已经过了结束时间，计算到明天开始时间的秒数
            tomorrow = now.replace(
                hour=self.start_time.hour,
                minute=self.start_time.minute,
                second=0,
                microsecond=0
            ) + timedelta(days=1)
            return int((tomorrow - now).total_seconds())
        else:
            # 当前在工作时间内
            return None
    
    def get_time_until_end(self) -> Optional[int]:
        """获取距离结束时间的秒数，如果不在工作时间内则返回None"""
        if not self.is_working_hours():
            return None
        
        now = datetime.now()
        end_datetime = now.replace(
            hour=self.end_time.hour,
            minute=self.end_time.minute,
            second=0,
            microsecond=0
        )
        
        return int((end_datetime - now).total_seconds())
    
    def start_process(self, process_name: str) -> bool:
        """启动指定的进程"""
        try:
            if process_name == 'producer':
                script_path = self.project_root / 'producer' / 'main.py'
                cmd = [
                    sys.executable, str(script_path),
                    '--env', self.environment
                ]
                title = f"Producer ({self.environment})"
            elif process_name == 'consumer':
                script_path = self.project_root / 'consumer' / 'main.py'
                cmd = [
                    sys.executable, str(script_path),
                    '--env', self.environment
                ]
                title = f"Consumer ({self.environment})"
            else:
                self.logger.error(f"未知的进程名称: {process_name}")
                return False
            
            # 检查脚本文件是否存在
            if not script_path.exists():
                self.logger.error(f"脚本文件不存在: {script_path}")
                return False
            
            # 在新窗口中启动进程
            if sys.platform == "win32":
                self.logger.error(f"System Windows ")
                # Windows系统
                full_cmd = ['cmd', '/c', 'start', f'"{title}"'] + cmd
            elif sys.platform == "darwin":
                # macOS系统
                applescript = f'''
                tell application "Terminal"
                    do script "cd {self.project_root} && {' '.join(cmd)}"
                    set custom title of front window to "{title}"
                end tell
                '''
                full_cmd = ['osascript', '-e', applescript]
            else:
                # Linux系统
                full_cmd = ['gnome-terminal', '--title', title, '--', 'bash', '-c', 
                           f"cd {self.project_root} && {' '.join(cmd)} && read -p 'Press Enter to close...'"]
            
            self.logger.info(f"启动 {process_name}: {' '.join(cmd)}")
            
            if sys.platform == "darwin":
                # macOS使用AppleScript启动
                process = subprocess.Popen(full_cmd, 
                                         stdout=subprocess.PIPE, 
                                         stderr=subprocess.PIPE)
            else:
                # Windows和Linux
                process = subprocess.Popen(full_cmd, 
                                         cwd=self.project_root,
                                         stdout=subprocess.PIPE, 
                                         stderr=subprocess.PIPE)
            
            self.processes[process_name] = process
            self.logger.info(f"{process_name} 进程已启动 (PID: {process.pid})")
            return True
            
        except Exception as e:
            self.logger.error(f"启动 {process_name} 失败: {e}")
            return False
    
    def is_process_running(self, process_name: str) -> bool:
        """检查进程是否正在运行"""
        process = self.processes.get(process_name)
        if process is None:
            return False
        
        # 检查进程是否仍在运行
        return process.poll() is None
    
    def stop_process(self, process_name: str):
        """停止指定的进程"""
        process = self.processes.get(process_name)
        if process is None:
            return
        
        try:
            self.logger.info(f"正在停止 {process_name} 进程...")
            
            # 尝试优雅关闭
            process.terminate()
            
            # 等待进程结束
            try:
                process.wait(timeout=10)
                self.logger.info(f"{process_name} 进程已优雅关闭")
            except subprocess.TimeoutExpired:
                # 强制杀死进程
                self.logger.warning(f"{process_name} 进程未响应，强制关闭")
                process.kill()
                process.wait()
                
        except Exception as e:
            self.logger.error(f"停止 {process_name} 进程时发生错误: {e}")
        finally:
            self.processes[process_name] = None

    def start_all_processes(self):
        """启动所有进程"""
        self.logger.info("启动所有进程...")

        success_count = 0
        for process_name in ['producer', 'consumer']:
            if self.start_process(process_name):
                success_count += 1
                # 给进程一些时间启动
                time.sleep(2)

        self.logger.info(f"成功启动 {success_count}/2 个进程")
        return success_count == 2

    def stop_all_processes(self):
        """停止所有进程"""
        self.logger.info("停止所有进程...")

        for process_name in ['producer', 'consumer']:
            self.stop_process(process_name)

    def check_and_restart_processes(self):
        """检查并重启已停止的进程"""
        if not self.is_working_hours():
            return

        for process_name in ['producer', 'consumer']:
            if not self.is_process_running(process_name):
                self.logger.warning(f"{process_name} 进程已停止，正在重启...")
                if self.start_process(process_name):
                    self.logger.info(f"{process_name} 进程重启成功")
                else:
                    self.logger.error(f"{process_name} 进程重启失败")

    def monitor_loop(self):
        """监控循环"""
        self.logger.info("开始监控循环...")

        while self.running:
            try:
                current_time = datetime.now().time()

                if self.is_working_hours():
                    # 在工作时间内，检查并重启进程
                    self.check_and_restart_processes()

                    # 显示状态信息
                    producer_status = "运行中" if self.is_process_running('producer') else "已停止"
                    consumer_status = "运行中" if self.is_process_running('consumer') else "已停止"

                    time_until_end = self.get_time_until_end()
                    if time_until_end:
                        hours, remainder = divmod(time_until_end, 3600)
                        minutes, _ = divmod(remainder, 60)
                        time_str = f"{hours:02d}:{minutes:02d}"
                    else:
                        time_str = "未知"

                    self.logger.info(f"状态检查 - Producer: {producer_status}, Consumer: {consumer_status}, "
                                   f"距离结束: {time_str}")
                else:
                    # 不在工作时间内，停止所有进程
                    any_running = any(self.is_process_running(name) for name in ['producer', 'consumer'])
                    if any_running:
                        self.logger.info("当前不在工作时间内，停止所有进程")
                        self.stop_all_processes()

                    # 计算下次启动时间
                    time_until_start = self.get_time_until_start()
                    if time_until_start:
                        hours, remainder = divmod(time_until_start, 3600)
                        minutes, _ = divmod(remainder, 60)
                        time_str = f"{hours:02d}:{minutes:02d}"
                        self.logger.info(f"等待下次启动时间，距离开始: {time_str}")

                # 等待下次检查
                time.sleep(self.check_interval)

            except Exception as e:
                self.logger.error(f"监控循环中发生错误: {e}")
                time.sleep(self.check_interval)

    def start(self):
        """启动主控制器"""
        self.logger.info("=" * 60)
        self.logger.info("🚀 主控制器启动")
        self.logger.info("=" * 60)
        self.logger.info(f"🌍 运行环境: {self.environment.upper()}")
        self.logger.info(f"⏰ 工作时间: {self.start_time.strftime('%H:%M')} - {self.end_time.strftime('%H:%M')}")
        self.logger.info(f"🔄 检查间隔: {self.check_interval} 秒")
        self.logger.info(f"📁 项目根目录: {self.project_root}")
        self.logger.info("=" * 60)

        self.running = True

        # 检查当前是否在工作时间内
        if self.is_working_hours():
            self.logger.info("当前在工作时间内，启动所有进程...")
            self.start_all_processes()
        else:
            time_until_start = self.get_time_until_start()
            if time_until_start:
                hours, remainder = divmod(time_until_start, 3600)
                minutes, _ = divmod(remainder, 60)
                self.logger.info(f"当前不在工作时间内，等待 {hours:02d}:{minutes:02d} 后启动")

        # 启动监控循环
        try:
            self.monitor_loop()
        except KeyboardInterrupt:
            self.logger.info("接收到键盘中断信号")
        except Exception as e:
            self.logger.error(f"主控制器运行时发生错误: {e}")
        finally:
            self.shutdown()

    def shutdown(self):
        """关闭主控制器"""
        if not self.running:
            return

        self.logger.info("开始关闭主控制器...")
        self.running = False

        # 停止所有进程
        self.stop_all_processes()

        self.logger.info("✅ 主控制器已关闭")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='市场数据主控制程序')
    parser.add_argument('--env', '--environment', default='dev',
                       choices=['dev', 'prod'],
                       help='运行环境 (dev: 开发环境, prod: 生产环境)')
    parser.add_argument('--check-interval', type=int, default=30,
                       help='进程检查间隔（秒），默认30秒')
    parser.add_argument('--start-time', default='08:50',
                       help='开始时间，格式HH:MM，默认08:50')
    parser.add_argument('--end-time', default='18:50',
                       help='结束时间，格式HH:MM，默认18:50')

    args = parser.parse_args()

    # 创建主控制器
    controller = MainController(
        environment=args.env,
        check_interval=args.check_interval
    )

    # 解析自定义时间
    try:
        if args.start_time != '08:50':
            hour, minute = map(int, args.start_time.split(':'))
            controller.start_time = dt_time(hour, minute)

        if args.end_time != '18:50':
            hour, minute = map(int, args.end_time.split(':'))
            controller.end_time = dt_time(hour, minute)
    except ValueError as e:
        print(f"时间格式错误: {e}")
        sys.exit(1)

    # 启动控制器
    controller.start()


if __name__ == '__main__':
    main()
