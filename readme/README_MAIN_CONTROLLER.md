# 主控制程序使用说明

## 概述

主控制程序 (`main_controller.py`) 是一个自动化管理工具，用于在指定时间段内启动和监控 producer 和 consumer 程序。

## 主要功能

1. **时间控制**: 只在指定时间段（默认 8:50-15:50）内运行程序
2. **自动启动**: 在工作时间内自动启动 producer 和 consumer 程序
3. **进程监控**: 定期检查进程状态，如果程序意外退出则自动重启
4. **多窗口运行**: 在不同的终端窗口中分别运行 producer 和 consumer
5. **优雅关闭**: 支持信号处理，可以优雅地停止所有进程
6. **轮询检测**: 持续监控程序状态，确保服务稳定运行

## 使用方法

### 基本用法

```bash
# 使用默认设置运行（开发环境，8:50-15:50）
python3 main_controller.py

# 指定生产环境
python3 main_controller.py --env prod

# 自定义时间范围
python3 main_controller.py --start-time 09:00 --end-time 16:00

# 自定义检查间隔（默认30秒）
python3 main_controller.py --check-interval 60
```

### 命令行参数

- `--env, --environment`: 运行环境 (dev/prod)，默认 dev
- `--check-interval`: 进程检查间隔（秒），默认 30
- `--start-time`: 开始时间，格式 HH:MM，默认 08:50
- `--end-time`: 结束时间，格式 HH:MM，默认 15:50

### 查看帮助

```bash
python3 main_controller.py --help
```

## 工作原理

1. **启动阶段**:
   - 检查当前时间是否在工作时间内
   - 如果在工作时间内，立即启动 producer 和 consumer 程序
   - 如果不在工作时间内，等待到开始时间

2. **监控阶段**:
   - 每隔指定间隔（默认30秒）检查一次
   - 在工作时间内：检查进程状态，如果进程停止则自动重启
   - 在工作时间外：停止所有正在运行的进程

3. **关闭阶段**:
   - 接收到停止信号时，优雅关闭所有进程
   - 清理资源并退出

## 日志记录

- 主控制程序日志：`logs/main_controller.log`
- Producer 日志：`logs/producer.log`
- Consumer 日志：`logs/single_thread_consumer.log`

## 配置文件路径修复

程序已自动修复了 producer 和 consumer 中的配置文件路径问题：

- **修复前**: 使用相对路径 `../config/dev/...`
- **修复后**: 使用项目根目录的绝对路径

这确保了无论从哪个目录运行程序，都能正确找到配置文件。

## 支持的操作系统

- **macOS**: 使用 AppleScript 在 Terminal 中打开新窗口
- **Windows**: 使用 `cmd` 命令打开新窗口
- **Linux**: 使用 `gnome-terminal` 打开新窗口

## 示例场景

### 场景1：正常工作日
```bash
# 早上8:50自动启动
python3 main_controller.py

# 程序会：
# 1. 在8:50自动启动producer和consumer
# 2. 每30秒检查一次进程状态
# 3. 如果程序意外退出，自动重启
# 4. 在15:50自动停止所有程序
```

### 场景2：自定义交易时间
```bash
# 自定义交易时间为9:00-15:00
python3 main_controller.py --start-time 09:00 --end-time 15:00
```

### 场景3：生产环境部署
```bash
# 生产环境，更频繁的检查（每10秒）
python3 main_controller.py --env prod --check-interval 10
```

## 故障排除

### 常见问题

1. **程序无法启动**
   - 检查 producer/main.py 和 consumer/main.py 是否存在
   - 检查配置文件是否存在于 config/dev/ 或 config/prod/ 目录

2. **进程频繁重启**
   - 检查 producer 和 consumer 程序的日志
   - 可能是配置问题或依赖服务（RabbitMQ、数据库）不可用

3. **新窗口无法打开**
   - macOS: 确保 Terminal 应用有权限
   - Linux: 确保安装了 gnome-terminal
   - Windows: 确保在 cmd 环境中运行

### 手动停止

如果需要手动停止主控制程序：

```bash
# 使用 Ctrl+C 优雅停止
# 或发送 SIGTERM 信号
kill -TERM <主控制程序PID>
```

## 安全注意事项

1. 确保只在受信任的环境中运行
2. 定期检查日志文件，监控异常情况
3. 在生产环境中使用适当的权限运行
4. 建议配置系统服务管理器（如 systemd）来管理主控制程序

## 更新历史

- v1.0: 初始版本，支持基本的时间控制和进程管理
- v1.1: 修复配置文件路径问题，支持多操作系统
- v1.2: 添加详细日志记录和错误处理
